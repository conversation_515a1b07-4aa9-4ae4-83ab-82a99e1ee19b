<template>
    <div class="calendar-view">
        <div class="calendar-header">
            <div class="day-header" v-for="date in dates" :key="date">
                <div class="day-name">{{ formatDayName(date) }}</div>
                <div class="day-number">{{ formatDayNumber(date) }}</div>
            </div>
        </div>

        <div class="calendar-body">
            <!-- AM Shift Row -->
            <div class="shift-row">
                <div class="shift-label">AM</div>
                <div v-for="date in dates" :key="`am-${date}`" class="shift-cell" @dragover.prevent
                    @drop="handleDrop(date, '1')">
                    <div class="planned-visits">
                        <div v-for="item in getPlannedVisitsForShift(date, '1')"
                            :key="`planned-${date}-${concatLevel(item)}-1`" class="planned-visit-wrapper">
                            <visit-card :item="item" :showRemove="!isVisitCompleted(date, item)"
                                @remove="$emit('removeVisit', date, item)" />

                            <div class="visit-controls" v-if="isVisitPlanned(date, item)">
                                <div class="time-input" v-if="plan_time === 'yes'">
                                    <input type="time" :value="getVisitTime(date, item)"
                                        @input="updateTime(item, date, $event.target.value)"
                                        :disabled="isVisitCompleted(date, item)" />
                                </div>

                                <div class="visit-status">
                                    <span v-if="isVisitCompleted(date, item)"
                                        class="status-badge completed">Completed</span>
                                    <span v-else class="status-badge pending">Pending</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PM Shift Row -->
            <div class="shift-row" v-if="plan_shift === 'yes'">
                <div class="shift-label">PM</div>
                <div v-for="date in dates" :key="`pm-${date}`" class="shift-cell" @dragover.prevent
                    @drop="handleDrop(date, '2')">
                    <div class="planned-visits">
                        <div v-for="item in getPlannedVisitsForShift(date, '2')"
                            :key="`planned-${date}-${concatLevel(item)}-2`" class="planned-visit-wrapper">
                            <visit-card :item="item" :showRemove="!isVisitCompleted(date, item)"
                                @remove="$emit('removeVisit', date, item)" />

                            <div class="visit-controls" v-if="isVisitPlanned(date, item)">
                                <div class="time-input" v-if="plan_time === 'yes'">
                                    <input type="time" :value="getVisitTime(date, item)"
                                        @input="updateTime(item, date, $event.target.value)"
                                        :disabled="isVisitCompleted(date, item)" />
                                </div>

                                <div class="visit-status">
                                    <span v-if="isVisitCompleted(date, item)"
                                        class="status-badge completed">Completed</span>
                                    <span v-else class="status-badge pending">Pending</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import VisitCard from './VisitCard.vue';
import { formatDayName, formatDayNumber } from '../../utils/dateUtils';

export default {
    name: 'CalendarView',
    components: {
        VisitCard
    },
    props: {
        dates: {
            type: Array,
            required: true
        },
        selected: {
            type: Object,
            required: true
        },
        planSelectedStatus: {
            type: Object,
            required: true
        },
        plan_shift: {
            type: String,
            default: 'no'
        },
        plan_time: {
            type: String,
            default: 'no'
        },
        plan_level: {
            type: Object,
            required: true
        }
    },
    emits: ['dropVisit', 'removeVisit', 'updateTime', 'updateShift'],
    methods: {
        formatDayName(dateStr) {
            return formatDayName(dateStr);
        },
        formatDayNumber(dateStr) {
            return formatDayNumber(dateStr);
        },
        concatLevel(item) {
            return item.account_dr_id ? `${item.id}_${item.account_dr_id}` : String(item.id);
        },
        handleDrop(date, shift) {
            this.$emit('dropVisit', date, shift);
        },
        getPlannedVisitsForShift(date, shift) {
            if (!this.selected[date]) return [];

            // Find all items that are planned for this date and shift
            const plannedItems = [];

            Object.keys(this.selected[date]).forEach(id => {
                const planData = this.selected[date][id];
                const isSelected = this.planSelectedStatus[date] && this.planSelectedStatus[date][id];

                if (isSelected && planData.line_id !== null && planData.shift_id === shift) {
                    // Find the original item data
                    const originalItem = this.findOriginalItem(id);
                    if (originalItem) {
                        plannedItems.push(originalItem);
                    }
                }
            });

            return plannedItems;
        },
        findOriginalItem(id) {
            // This method finds the original item data based on the id
            // The id format is either 'account_id' or 'account_id_account_dr_id'
            const parts = id.split('_');

            if (this.plan_level.level === 'Doctor' && parts.length > 1) {
                const account_id = parts[0];
                const account_dr_id = parts[1];

                // Find the item with matching account_dr_id
                return this.$parent.items.find(item =>
                    item.account_dr_id && item.account_dr_id.toString() === account_dr_id);
            } else {
                // Find the item with matching id
                return this.$parent.items.find(item => item.id.toString() === parts[0]);
            }
        },
        isVisitPlanned(date, item) {
            const id = this.concatLevel(item);
            return this.selected[date] &&
                this.selected[date][id] &&
                this.selected[date][id].line_id !== null &&
                this.planSelectedStatus[date] &&
                this.planSelectedStatus[date][id];
        },
        isVisitCompleted(date, item) {
            const id = this.concatLevel(item);
            return this.selected[date] &&
                this.selected[date][id] &&
                this.selected[date][id].old;
        },
        getVisitTime(date, item) {
            const id = this.concatLevel(item);
            if (this.selected[date] && this.selected[date][id]) {
                return this.selected[date][id].time || '';
            }
            return '';
        },
        updateTime(item, date, timeValue) {
            this.$emit('updateTime', item, date, timeValue);
        }
    }
}
</script>

<style scoped>
.calendar-view {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.calendar-header {
    display: flex;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: #f8fafc;
}

.day-header {
    flex: 1;
    text-align: center;
    padding: 12px;
    border-right: 1px solid rgba(0, 0, 0, 0.05);
}

.day-header:last-child {
    border-right: none;
}

.day-name {
    font-weight: 600;
    color: var(--primary-dark);
    font-size: 14px;
}

.day-number {
    font-size: 20px;
    font-weight: 700;
    color: var(--dark);
    margin-top: 4px;
}

.calendar-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.shift-row {
    display: flex;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    min-height: 200px;
    flex: 1;
}

.shift-label {
    width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8fafc;
    font-weight: 600;
    color: var(--primary-dark);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
}

.shift-cell {
    flex: 1;
    padding: 12px;
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    min-height: 200px;
    overflow-y: auto;
}

.shift-cell:last-child {
    border-right: none;
}

.planned-visits {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.planned-visit-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.visit-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0 8px;
}

.time-input input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 12px;
}

.time-input input:disabled {
    background: #f8fafc;
    cursor: not-allowed;
}

.visit-status {
    display: flex;
    justify-content: center;
}

.status-badge {
    font-size: 11px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.status-badge.completed {
    background: var(--success-light);
    color: var(--success);
}

.status-badge.pending {
    background: var(--warning-light);
    color: var(--warning-dark);
}
</style>