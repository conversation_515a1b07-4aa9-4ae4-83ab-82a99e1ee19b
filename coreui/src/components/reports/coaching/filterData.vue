<template>
  <c-card>
    <c-card-header> Coaching Statistics Report </c-card-header>
    <c-card-body>
      <c-row>
        <c-col lg="4" md="4" sm="8">
          <c-input
            label="From"
            type="date"
            placeholder="From"
            v-model="from_date"
          />
        </c-col>
        <c-col lg="4" md="4" sm="8">
          <c-input
            label="To"
            type="date"
            placeholder="To"
            v-model="to_date"
            @input="getAllData"
          />
        </c-col>
        <c-col lg="4" md="4" sm="8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input
                label="All"
                v-if="lines.length != 0"
                id="line"
                class="m-1"
                type="checkbox"
                v-model="checkAllLines"
                title="Check All lines"
                @change="toggleCheckAllLines"
              />
              <label
                for="line"
                v-if="lines.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="line_id"
                :options="lines"
                label="name"
                :value="0"
                :reduce="(line) => line.id"
                placeholder="Select Line"
                class="mt-2"
                multiple
                @input="getLineData"
              />
            </template>
          </c-form-group>
        </c-col>
        <!-- <c-col lg="4" md="4" sm="8">
          <c-form-group>
            <template #label>Division</template>
            <template #input>
              <input
                label="All"
                id="division"
                v-if="divisions.length != 0"
                class="m-1"
                type="checkbox"
                v-model="checkAllDivisions"
                title="Check All Divisions"
                @change="toggleCheckAllDivs"
                :disabled="user_id.length ? true : false"
              />
              <label
                for="division"
                v-if="divisions.length != 0"
                style="font-weight: bold"
              >
                All
              </label>
              <v-select
                v-model="division_id"
                :options="divisions"
                label="name"
                :value="0"
                :reduce="(division) => division.id"
                placeholder="Select Division"
                class="mt-2"
                multiple
                :disabled="user_id.length ? true : false"
              />
            </template>
          </c-form-group>
        </c-col> -->
        <c-col lg="4" md="4" sm="8">
          <c-form-group>
            <template #label> Employee </template>
            <template #input>
              <input
                label="All"
                v-if="users.length != 0"
                id="user"
                class="m-1"
                type="checkbox"
                v-model="checkAllEmployees"
                title="check All Employees"
                @change="toggleCheckAllEmployees"
                :disabled="division_id.length ? true : false"
              />
              <label
                for="user"
                v-if="users.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="user_id"
                :options="users"
                label="fullname"
                :value="0"
                :reduce="(user) => user.id"
                placeholder="Select Employee"
                class="mt-2"
                multiple
                :disabled="division_id.length ? true : false"
              />
            </template>
          </c-form-group>
        </c-col>
        <c-col lg="4" md="4" sm="4">
          <c-form-group>
            <template #label>Type</template>
            <template #input>
              <input
                label="All"
                v-if="users.length != 0"
                id="type"
                class="m-1"
                type="checkbox"
                v-model="checkAllTypes"
                title="check All Types"
                @change="toggleCheckAllTypes"
              />
              <label
                for="type"
                v-if="types.length != 0"
                style="font-weight: bold"
                >All</label
              >
              <v-select
                v-model="type_id"
                :options="types"
                label="name"
                :value="0"
                :reduce="(type) => type.id"
                placeholder="Select Type"
                class="mt-2"
                multiple
              />
            </template>
          </c-form-group>
        </c-col>
      </c-row>
    </c-card-body>
    <c-card-footer>
      <c-button
        v-if="line_id != null"
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        >Show</c-button
      >
    </c-card-footer>
  </c-card>
</template>

<script>
import axios from "axios";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapState } from "vuex";
export default {
  components: {
    vSelect,
  },
  emits: ["getStatistics"],
  data() {
    return {
      evaluator_id: null,
      line_id: [],
      lines: [],
      division_id: [],
      divisions: [],
      checkAllDivisions: false,
      user_id: [],
      users: [],
      checkAllEmployees: false,
      type_id: [],
      types: [],
      checkAllTypes: false,
      checkAllLines: false,
      from_date: new Date().toISOString().slice(0, 10),
      to_date: new Date().toISOString().slice(0, 10),
      // selectedLine: "",
    };
  },
  computed: {
    ...mapState("authentication", ["authUser"]),
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ["lines", "users"],
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.users = response.data.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ["divisions", "users", "coachingTypes"],
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
          this.types = response.data.data.coachingTypes;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getAllData() {
      this.line_id = [];
      this.division_id = [];
      this.user_id = [];
      this.type_id = [];
      this.initialize();
    },
    toggleCheckAllLines() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) {
        this.line_id = [];
        this.user_id = [];
        this.type_id = [];
      }
      this.getLineData();
    },
    // toggleCheckAllDivs() {
    //   if (!this.checkAllDivisions) this.division_id = [];
    //   else this.division_id = this.divisions.map((item) => item.id);
    // },
    toggleCheckAllEmployees() {
      if (!this.checkAllEmployees) this.user_id = [];
      else this.user_id = this.users.map((item) => item.id);
    },
    toggleCheckAllTypes() {
      if (!this.checkAllTypes) this.type_id = [];
      else this.type_id = this.types.map((item) => item.id);
    },
    show() {
      let statsFilterData = {
        line_id: this.line_id,
        divisions: this.division_id,
        users: this.user_id,
        type_ids: this.type_id,
        from_date: this.from_date,
        to_date: this.to_date,
        evaluator_id: this.authUser.id,
        // line: this.selectedLine,
      };
      this.$emit("getStatistics", { statsFilterData });
    },
    // getLineData() {
    //   axios
    //     .get(`/api/coaching/report/${this.line_id}`)
    //     .then((response) => {
    //       this.divisions = response.data.divisions;
    //       this.users = response.data.users;
    //       this.types = response.data.types;
    //       this.selectedLine = response.data.line;
    //     })
    //     .catch((error) => this.showErrorMessage(error));
    // },
  },
  created() {
    this.initialize();
  },
};
</script>
