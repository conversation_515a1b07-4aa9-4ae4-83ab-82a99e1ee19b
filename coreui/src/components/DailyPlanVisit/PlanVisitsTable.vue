<template>
    <div class="table-container">
        <table v-if="initialized" class="data-table">
            <thead>
                <tr>
                    <th v-for="field in fieldsWithoutDates" :key="field" @click="$emit('sortBy', field)"
                        class="sortable-header"
                        :class="{ 'sorted-asc': sortColumn === field && sortDirection === 'asc', 'sorted-desc': sortColumn === field && sortDirection === 'desc' }">
                        {{ formatFieldName(field) }}
                        <c-icon v-if="sortColumn === field" :name="getSortIcon(field, 'alpha')"></c-icon>
                    </th>
                    <th v-for="date in dates" :key="date" class="date-column">
                        <div class="date-header">
                            <span class="day-name">{{ formatDayName(date) }}</span>
                            <span class="day-number">{{ formatDayNumber(date) }}</span>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item, index) in sortedItems" :key="`item-${index}`" class="data-row">
                    <td v-for="field in fieldsWithoutDates" :key="`${item.id}-${field}`"
                        :class="getCellClass(item, field)">
                        <div v-if="field === 'account'" class="account-cell">
                            <span
                                :class="{ 'over-frequency': item.frequency && item.frequency <= item.monthly_actual }">
                                {{ item.account }}
                            </span>
                        </div>
                        <div v-else-if="field === 'group_name'" class="group-cell"
                            :style="{ background: item.group_color }">
                            {{ item.group_name }}
                        </div>
                        <div v-else-if="field === 'doctor'" class="doctor-cell">
                            <span
                                :class="{ 'over-frequency': item.frequency && item.frequency <= item.monthly_actual }">
                                {{ item.doctor }}
                            </span>
                        </div>
                        <div v-else-if="field === 'pharmacies'" class="pharmacy-cell">
                            <button @click="$emit('getLinkedPharmacies', item)" class="pharmacy-btn"
                                :class="{ 'warning': item.pharmacies <= 0 }">
                                {{ item.pharmacies }}
                            </button>
                        </div>
                        <div v-else>{{ item[field] }}</div>
                    </td>

                    <td v-for="date in dates" :key="`${item.id}-${date}`" class="plan-cell">
                        <div class="plan-controls">
                            <label class="plan-toggle">
                                <input type="checkbox"
                                    :checked="planSelectedStatus[date] && planSelectedStatus[date][concatLevel(item)]"
                                    @change="$emit('togglePlanSelection', $event, item, date)" :disabled="selected[date] &&
                                        selected[date][concatLevel(item)] &&
                                        selected[date][concatLevel(item)].old" />
                                <span class="toggle-slider"></span>
                            </label>

                            <div class="plan-details"
                                v-if="selected[date] && selected[date][concatLevel(item)] && selected[date][concatLevel(item)].line_id !== null">
                                <div class="shifts"
                                    v-if="plan_shift === 'yes' && selected[date][concatLevel(item)].line_id !== null">
                                    <label class="shift-option"
                                        :class="{ 'active': selected[date][concatLevel(item)].shift_id === '1' }">
                                        <input type="radio" :value="'1'"
                                            :checked="selected[date][concatLevel(item)].shift_id === '1'"
                                            @change="updateShift(item, date, '1')"
                                            :disabled="selected[date][concatLevel(item)].old" />
                                        <span>AM</span>
                                    </label>
                                    <label class="shift-option"
                                        :class="{ 'active': selected[date][concatLevel(item)].shift_id === '2' }">
                                        <input type="radio" :value="'2'"
                                            :checked="selected[date][concatLevel(item)].shift_id === '2'"
                                            @change="updateShift(item, date, '2')"
                                            :disabled="selected[date][concatLevel(item)].old" />
                                        <span>PM</span>
                                    </label>
                                </div>

                                <div class="time-input" v-if="plan_time === 'yes'">
                                    <input type="time" :value="selected[date][concatLevel(item)].time"
                                        @input="updateTime(item, date, $event.target.value)"
                                        :disabled="selected[date][concatLevel(item)].old" />
                                </div>

                                <div class="visit-status">
                                    <span v-if="selected[date][concatLevel(item)].old"
                                        class="status-badge completed">Completed</span>
                                    <span v-else class="status-badge pending">Pending</span>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
import { formatDayName, formatDayNumber } from '../../utils/dateUtils';

export default {
    name: 'PlanVisitsTable',
    props: {
        items: { type: Array, required: true },
        fieldsWithoutDates: { type: Array, required: true },
        dates: { type: Array, required: true },
        selected: { type: Object, required: true },
        planSelectedStatus: { type: Object, required: true },
        plan_shift: { type: String, default: 'no' },
        plan_time: { type: String, default: 'no' },
        plan_level: { type: Object, required: true },
        sortColumn: { type: String, default: null },
        sortDirection: { type: String, default: 'asc' },
        initialized: { type: Boolean, default: false },
        from: { type: String, required: true } // Add the 'from' prop
    },
    emits: ['sortBy', 'getLinkedPharmacies', 'togglePlanSelection', 'update:selected'],
    computed: {
        sortedItems() {
            if (!this.sortColumn) return this.items;
            return [...this.items].sort((a, b) => {
                const modifier = this.sortDirection === 'asc' ? 1 : -1;
                const aValue = a[this.sortColumn];
                const bValue = b[this.sortColumn];
                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return (aValue - bValue) * modifier;
                }
                return String(aValue).localeCompare(String(bValue)) * modifier;
            });
        }
    },
    methods: {
        formatFieldName(field) {
            if (!field) return '';
            const withSpaces = field.replace(/[_-]/g, ' ');
            return withSpaces.replace(/\b\w/g, c => c.toUpperCase());
        },
        concatLevel(item) {
            return item.account_dr_id ? `${item.id}_${item.account_dr_id}` : String(item.id);
        },
        getCellClass(item, field) {
            if (field === 'account' || field === 'doctor') {
                return {
                    'warning-cell': item.frequency && item.frequency <= item.monthly_actual
                };
            }
            if (field === 'pharmacies') {
                return { 'warning': item.pharmacies <= 0 };
            }
            return {};
        },
        formatDayName(dateStr) {
            return formatDayName(dateStr, this.from, { uppercase: true });
        },
        formatDayNumber(dateStr) {
            return formatDayNumber(dateStr, this.from);
        },
        getSortIcon(key, type = 'numeric') {
            // Assuming sortKey and sortOrder are managed by parent now
            // This might need adjustment based on how parent handles sorting state
            if (this.sortColumn !== key) return `cil-sort-${type}-down`;
            return this.sortDirection === 'asc' ? `cil-sort-${type}-up` : `cil-sort-${type}-down`;
        },
        updateShift(item, date, shiftValue) {
            const id = this.concatLevel(item);
            const updatedSelected = JSON.parse(JSON.stringify(this.selected)); // Deep clone
            if (updatedSelected[date] && updatedSelected[date][id]) {
                updatedSelected[date][id].shift_id = shiftValue;
                this.$emit('update:selected', updatedSelected);
            }
        },
        updateTime(item, date, timeValue) {
            const id = this.concatLevel(item);
            const updatedSelected = JSON.parse(JSON.stringify(this.selected)); // Deep clone
            if (updatedSelected[date] && updatedSelected[date][id]) {
                updatedSelected[date][id].time = timeValue;
                this.$emit('update:selected', updatedSelected);
            }
        }
    }
}
</script>

<style scoped>
/* Add relevant styles extracted from the original component */
.table-container {
    overflow-x: auto;
    overflow-y: auto;
    padding: 8px;
    max-height: 70vh;
    /* Consider making this configurable via prop */
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 14px;
}

.data-table thead th {
    position: sticky;
    top: 0;
    background: var(--light);
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: var(--dark);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.sortable-header {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.sortable-header:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.sortable-header.sorted-asc,
.sortable-header.sorted-desc {
    color: var(--primary);
}

.date-column {
    min-width: 100px;
    border-left: 1px dashed rgba(0, 0, 0, 0.1);
}

.date-header {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.day-name {
    font-size: 12px;
    color: var(--gray);
    text-transform: uppercase;
}

.day-number {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-dark);
}

.data-row:hover td {
    background-color: var(--primary-light);
}

.data-table td {
    padding: 10px 16px;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

.account-cell span.over-frequency,
.doctor-cell span.over-frequency,
.warning-cell {
    color: var(--warning);
    font-weight: 600;
}

.group-cell {
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    display: inline-block;
    min-width: 60px;
}

.pharmacy-cell .pharmacy-btn {
    background: none;
    border: 1px solid var(--gray);
    color: var(--dark);
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.pharmacy-cell .pharmacy-btn:hover {
    background: var(--primary-light);
    border-color: var(--primary);
    color: var(--primary-dark);
}

.pharmacy-cell .pharmacy-btn.warning {
    border-color: var(--warning);
    color: var(--warning);
}

.pharmacy-cell .pharmacy-btn.warning:hover {
    background: rgba(255, 126, 66, 0.1);
}

.plan-cell {
    text-align: center;
}

.plan-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.plan-toggle {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.plan-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 20px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

/* Pending state: Checked but not disabled */
input:checked:not(:disabled)+.toggle-slider {
    background-color: var(--warning);
}

/* Completed state: Disabled (implies checked based on logic) */
input:disabled+.toggle-slider {
    background-color: var(--success);
    opacity: 0.6;
    /* Keep the disabled look consistent */
    cursor: not-allowed;
}

input:checked+.toggle-slider:before {
    transform: translateX(20px);
}

.plan-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    margin-top: 4px;
}

.shifts {
    display: flex;
    gap: 4px;
    background: #f0f0f0;
    border-radius: 6px;
    padding: 2px;
}

.shift-option {
    padding: 2px 6px;
    font-size: 11px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
    display: flex;
    align-items: center;
}

.shift-option input[type="radio"] {
    display: none;
}

.shift-option span {
    pointer-events: none;
    /* Allow clicking label to trigger radio */
}

.shift-option.active {
    background-color: var(--primary);
    color: white;
}

.shift-option:not(.active):hover {
    background-color: #e0e0e0;
}

input[type="radio"]:disabled+span {
    opacity: 0.7;
    cursor: not-allowed;
}

.time-input input[type="time"] {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 2px 4px;
    font-size: 12px;
    width: 80px;
}

input[type="time"]:disabled {
    background-color: #f0f0f0;
    cursor: not-allowed;
    opacity: 0.7;
}

.visit-status {
    margin-top: 4px;
}

.status-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.status-badge.completed {
    background-color: rgba(12, 206, 107, 0.1);
    color: var(--success);
}

.status-badge.pending {
    background-color: rgba(255, 193, 7, 0.1);
    /* warning background */
    color: var(--warning);
    /* warning text */
}

/* Add CIcon styles if not globally available */
</style>