/**
 * Date utility functions for cross-browser compatibility
 * Specifically addresses Firefox date parsing issues
 */

/**
 * Parse a date string with Firefox compatibility
 * @param {string} dateStr - Date string to parse
 * @param {string|Date} referenceDate - Reference date to extract year from (optional)
 * @returns {Date} Parsed date object
 */
export function parseDate(dateStr, referenceDate = null) {
  if (!dateStr) return new Date(NaN);
  
  // Handle different date formats
  let date;
  
  if (dateStr.includes('-') && dateStr.split('-').length === 2) {
    // Format like "28-Jul" - add year for proper parsing
    const year = referenceDate ? new Date(referenceDate).getFullYear() : new Date().getFullYear();
    const dateWithYear = `${dateStr}-${year}`;
    date = new Date(dateWithYear);
  } else {
    // Other formats
    date = new Date(dateStr);
  }
  
  return date;
}

/**
 * Format day name from date string with Firefox compatibility
 * @param {string} dateStr - Date string to format
 * @param {string|Date} referenceDate - Reference date to extract year from (optional)
 * @param {Object} options - Formatting options
 * @returns {string} Formatted day name
 */
export function formatDayName(dateStr, referenceDate = null, options = {}) {
  const { locale = 'en-US', style = 'short', uppercase = false } = options;
  
  if (!dateStr) return '';
  
  const date = parseDate(dateStr, referenceDate);
  
  if (isNaN(date.getTime())) {
    console.error('Invalid date in formatDayName:', dateStr);
    return 'N/A';
  }
  
  const dayName = date.toLocaleDateString(locale, { weekday: style });
  return uppercase ? dayName.toUpperCase() : dayName;
}

/**
 * Format day number from date string with Firefox compatibility
 * @param {string} dateStr - Date string to format
 * @param {string|Date} referenceDate - Reference date to extract year from (optional)
 * @returns {string|number} Formatted day number
 */
export function formatDayNumber(dateStr, referenceDate = null) {
  if (!dateStr) return '';
  
  const date = parseDate(dateStr, referenceDate);
  
  if (isNaN(date.getTime())) {
    console.error('Invalid date in formatDayNumber:', dateStr);
    // Try to extract day number from string as fallback
    const dayMatch = dateStr.match(/^(\d{1,2})/);
    return dayMatch ? dayMatch[1] : '';
  }
  
  return date.getDate();
}

/**
 * Check if a date string is valid
 * @param {string} dateStr - Date string to validate
 * @param {string|Date} referenceDate - Reference date to extract year from (optional)
 * @returns {boolean} True if valid date
 */
export function isValidDate(dateStr, referenceDate = null) {
  if (!dateStr) return false;
  const date = parseDate(dateStr, referenceDate);
  return !isNaN(date.getTime());
}

/**
 * Format date with fallback for invalid dates
 * @param {string} dateStr - Date string to format
 * @param {string|Date} referenceDate - Reference date to extract year from (optional)
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date or fallback value
 */
export function formatDateSafe(dateStr, referenceDate = null, options = {}) {
  const { 
    locale = 'en-US', 
    dateStyle = 'short',
    fallback = '-'
  } = options;
  
  if (!dateStr) return fallback;
  
  const date = parseDate(dateStr, referenceDate);
  
  if (isNaN(date.getTime())) {
    console.error('Invalid date in formatDateSafe:', dateStr);
    return fallback;
  }
  
  return date.toLocaleDateString(locale, { dateStyle });
}
