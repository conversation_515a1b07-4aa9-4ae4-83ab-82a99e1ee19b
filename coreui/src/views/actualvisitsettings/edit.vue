<template>
  <c-row>
    <c-col col="12" lg="12">
      <c-card no-header>
        <c-card-body>
          <c-form>
            <template slot="header">
              Edit Actual Visit Setting id: {{ $route.params.id }}
            </template>
            <div class="row">
              <div class="col-6">
                <c-input label="Name" type="text" placeholder="Name" v-model="actual_visit_setting.name"
                  disabled="disabled">
                  <template slot="label">
                    Name <span style="color: red">*</span>
                  </template>
                </c-input>
              </div>
              <div class="col-6">
                <c-input label="Key" type="text" placeholder="Key" v-model="actual_visit_setting.key"
                  disabled="disabled">
                  <template slot="label">
                    Key <span style="color: red">*</span>
                  </template>
                </c-input>
              </div>
            </div>

            <div class="row">
              <div class="col-6">
                <c-input v-if="
                  inArray(actual_visit_setting.type, [
                    'text',
                    'number',
                    'email',
                    'file',
                    'url',
                  ])
                " label="Value" :type="actual_visit_setting.type" placeholder="Value"
                  v-model="actual_visit_setting.value"></c-input>

                <c-textarea v-if="actual_visit_setting.type == 'textarea'" label="Value" placeholder="Value"
                  v-model="actual_visit_setting.value"></c-textarea>

                <c-input v-if="
                  actual_visit_setting.type == 'date' &&
                  actual_visit_setting.key == 'specific_actual_start_day'
                " label="Value" :type="actual_visit_setting.type" placeholder="Value"
                  v-model="actual_visit_setting.value"></c-input>

                <c-form-group v-if="
                  actual_visit_setting.type == 'select' &&
                  actual_visit_setting.key == 'actual_visit_level'
                ">
                  <template #label> Value </template>
                  <template #input>
                    <v-select title="Value" v-model="actual_visit_setting.value"
                      :options="['Product', 'Brand', 'Brief']" placeholder="Select Value" class="mt-2" />
                  </template>
                </c-form-group>

                <div class="col" v-if="
                  actual_visit_setting.type == 'checkbox' &&
                  actual_visit_setting.key == 'public_holidays'
                ">
                  <label>Possibility to enter actual in public holidays</label><br />
                  <input type="checkbox" :id="`public_holidays`" @click="set()" :checked="checkedPublicHoliday()" />
                </div>

                <div class="col" v-if="
                  actual_visit_setting.type == 'checkbox' &&
                  actual_visit_setting.key == 'off_days'
                ">
                  <label>Possibility to enter actual in off days</label><br />
                  <input type="checkbox" :id="`off_days`" @click="setoffdays()" :checked="checkedOffDays()" />
                </div>

                <c-form-group v-if="
                  actual_visit_setting.type == 'select' &&
                  actual_visit_setting.key == 'actual_start_day'
                ">
                  <template #label> Value </template>
                  <template #input>
                    <v-select title="Value" v-model="actual_visit_setting.value" :options="actual_start_days"
                      label="name" :reduce="(startday) => startday.name" placeholder="Select option" class="mt-2" />
                  </template>
                </c-form-group>
                <c-form-group v-if="
                  actual_visit_setting.type == 'date' &&
                  actual_visit_setting.key == 'actual_extra_time'
                ">
                  <template #label> Add Extra Hours </template>
                  <template #input>
                    <c-input type="number" placeholder="Enter Hours Number"
                      v-model="actual_visit_setting.value"></c-input>
                  </template>
                </c-form-group>
                <c-form-group v-if="
                  actual_visit_setting.type == 'select' &&
                  (actual_visit_setting.key == 'copy_actual_visit' ||
                    actual_visit_setting.key == 'presentation_actual_visit' ||
                    actual_visit_setting.key ==
                    'accept_actual_with_detailing' ||
                    actual_visit_setting.key ==
                    'accept_visits_within_vacations' ||
                    actual_visit_setting.key ==
                    'accept_officework_with_visits' ||
                    actual_visit_setting.key ==
                    'actual_extra_time_with_vacations' ||
                    actual_visit_setting.key ==
                    'accept_single_visit_with_manager_double_plan' ||
                    actual_visit_setting.key == 'allow_actual_with_deviation' ||
                    actual_visit_setting.key == 'actual_date_disabled' ||
                    actual_visit_setting.key == 'ow_with_location' ||
                    actual_visit_setting.key == 'multiple_doctors_visit' ||
                    actual_visit_setting.key == 'actual_after_meet_frequency' ||
                    actual_visit_setting.key == 'add_shift' ||
                    actual_visit_setting.key == 'add_order' ||
                    actual_visit_setting.key == 'add_stock' ||
                    actual_visit_setting.key == 'add_pharmacy_type' ||
                    actual_visit_setting.key == 'add_more_than_one_ow_at_same_day' ||
                    actual_visit_setting.key == 'visits_call_rate_with_shift' ||
                    actual_visit_setting.key == 'visits_employee_tracking_with_shift'
                  )
                ">
                  <template #label> Value </template>
                  <template #input>
                    <v-select title="Value" v-model="actual_visit_setting.value" :options="actual_options" label="name"
                      placeholder="Select Value" class="mt-2" />
                  </template>
                </c-form-group>
              </div>

              <div class="col-6">
                <c-form-group>
                  <template #label> Type </template>
                  <template #input>
                    <v-select title="Type" v-model="actual_visit_setting.type" :options="types"
                      placeholder="Select Type" class="mt-2" disabled="disabled" />
                  </template>
                </c-form-group>
              </div>
            </div>
          </c-form>
        </c-card-body>
        <c-card-footer>
          <c-button class="text-white" color="primary" @click="update()">Update</c-button>
          <c-button color="default" :to="{ name: 'actual-visit-settings' }">Cancel</c-button>
        </c-card-footer>
      </c-card>
    </c-col>
  </c-row>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  name: "EditActualVisitSetting",
  components: {
    vSelect,
  },
  data() {
    return {
      actual_visit_setting: {
        name: "",
        key: "",
        value: "",
        type: "text",
      },
      actual_options: ["No", "Yes"],
      types: [
        "text",
        "number",
        "email",
        "date",
        "textarea",
        "map",
        "select",
        "file",
        "url",
        "checkbox",
      ],
      actual_start_days: [],
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/actualvisitsettings/${this.$route.params.id}/edit`)
        .then((response) => {
          this.actual_start_days = response.data.actual_start_days;
          this.actual_visit_setting = response.data.actual_visit_setting;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    set() {
      if (document.getElementById("public_holidays").checked) {
        this.actual_visit_setting.value = "Yes";
      } else {
        this.actual_visit_setting.value = "No";
      }
    },
    checkedPublicHoliday() {
      if (this.actual_visit_setting.value == "Yes") {
        return true;
      }
      return false;
    },
    setoffdays() {
      if (document.getElementById("off_days").checked) {
        this.actual_visit_setting.value = "Yes";
      } else {
        this.actual_visit_setting.value = "No";
      }
    },
    checkedOffDays() {
      if (this.actual_visit_setting.value == "Yes") {
        return true;
      }
      return false;
    },
    inArray(needle, haystack) {
      var length = haystack.length;
      for (var i = 0; i < length; i++) {
        if (haystack[i] == needle) return true;
      }
      return false;
    },
    update() {
      axios
        .put(`/api/actualvisitsettings/${this.$route.params.id}`, {
          id: this.actual_visit_setting.id,
          name: this.actual_visit_setting.name,
          key: this.actual_visit_setting.key,
          value: this.actual_visit_setting.value,
          type: this.actual_visit_setting.type,
        })
        .then((response) => {
          this.flash("Actual Visit Setting Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
