<template>
  <div>
    <filter-for-approvals @getPvs="getPvs" />
    <c-card v-if="showTable">
      <c-card-header><strong>Create Approval PV</strong></c-card-header>
      <c-card-body>
        <c-form-group>
          <template #input>
            <input class="m-1" id="checkAllPvs" type="checkbox" v-model="checkAllPvs" title="Check All Pvs"
              @change="checkAllPv" />
            <label for="checkAllPvs">Select All</label>
          </template>
        </c-form-group>
        <c-data-table hover striped sorter tableFilter footer itemsPerPageSelect :items="pvs" :fields="pvFields"
          :items-per-page="1000" :active-page="1" :responsive="true" table-filter pagination thead-top>
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ pvs.length }}
            </td>
          </template>
          <template #s="{ item }">
            <td>
              <input class="m-1" type="checkbox" v-model="selected" :value="item" title="Check One PV" />
            </td>
          </template>
          <template #actions="{ item }">
            <td>
              <div class="row" style="margin-left: 0px; margin-right: 0px">
                <div class="col">
                  <v-select v-model="item.reason_id" :options="item.reasons" :reduce="(reason) => reason.id"
                    label="reason" :value="0" placeholder="Select Reason" v-if="item.approval == null" class="mt-0" />
                </div>
              </div>
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <c-button title="Approve" color="primary" style="float: right" square @click="accept">Approve
        </c-button>
        <c-button class="mr-2" style="float: right" title="Reject" color="danger" square @click="reject">Disapprove
        </c-button>
      </c-card-footer>
    </c-card>
  </div>
</template>

<script>
import FilterForApprovals from "../../components/approvals/PvApprovalsFilter.vue";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
    FilterForApprovals,
  },
  data() {
    return {
      pvs: [],
      pvFields: [],
      selectPv: null,
      reasons: [],
      showTable: false,
      checkAllPvs: false,
      checkAllOwVisits: false,
      selected: [],
      selectedOW: [],
    };
  },
  methods: {
    getPvs(pvApproval) {
      this.selectPv = pvApproval;
      axios
        .post(`/api/get-pvs/`, pvApproval)
        .then((response) => {
          this.showTable = true;
          this.pvs = response.data.data.pvs;
          this.pvFields = response.data.data.pvFields;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllPv() {
      this.selected = [];
      if (this.checkAllPvs) {
        for (let i in this.pvs) {
          this.selected.push(this.pvs[i]);
        }
      }
    },
    reject() {
      let pvs = this.selected.map((item) => {
        return {
          visitable_id: item.id,
          visitable_type: item.visitable_type,
          reason_id: item.reason_id,
          required: item.required,
        };
      });
      axios
        .post("/api/pv/reject/", {
          pvs,
        })
        .then((response) => {
          this.selected = [];
          this.checkAllPvs = false;
          this.flash("Pv Disapproved Successfully");
          this.getPvs(this.selectPv);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    accept() {
      let pvs = this.selected.map((item) => {
        return {
          visitable_id: item.id,
          visitable_type: item.visitable_type,
          reason_id: item.reason_id,
          required: item.required,
        };
      });
      axios
        .post("/api/pv/accept/", {
          pvs,
        })
        .then(() => {
          this.selected = [];
          this.checkAllPvs = false;
          this.flash("Pv Approved Successfully");
          this.getPvs(this.selectPv);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>
