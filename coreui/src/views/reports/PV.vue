<template>
  <c-card v-if="visit">
    <c-card-header
      ><strong>
        <c-icon class="icon" name="cil-columns" /> PV at Visit ID :
        {{ $route.params.id }}
      </strong>
    </c-card-header>
    <c-card-body>
      <c-card>
        <c-card-header
          ><strong
            ><c-icon class="icon" name="cil-user" /> Submission Date
          </strong></c-card-header
        >
        <c-card-body>
          <div class="row">
            <div class="col-lg-6 col-md-6 col-sm-12">
              <strong>
                Received From Source Date <span style="color: red">*</span>
              </strong>
              <p>
                <strong>
                  {{ visit.actual_visit_date }}
                </strong>
              </p>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Type <span style="color: red">*</span>
                </template>
                <template #input>
                  <v-select
                    v-model="pv.pv_module_id"
                    :options="pvModules"
                    placeholder="Select Type"
                    label="name"
                    :reduce="(pvModule) => pvModule.id"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
          </div>
        </c-card-body>
      </c-card>
      <c-card>
        <c-card-header
          ><strong
            ><c-icon class="icon" name="cil-medical-cross" /> {{ visit.Doctor }}
          </strong>
          <strong>Speciality: {{ visit.Speciality }}</strong>
          <strong>Brick: {{ visit.Brick }}</strong>
        </c-card-header>
        <c-card-body>
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Qualification <span style="color: red">*</span>
                </template>
                <template #input>
                  <v-select
                    v-model="doctorInfo.qualification_id"
                    :options="doctorQualifications"
                    label="name"
                    :reduce="(doctorQualification) => doctorQualification.id"
                    placeholder="Select Qualification"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label"> Email </template>
                <template #input>
                  <c-input
                    type="text"
                    v-model="doctorInfo.email"
                    placeholder="Type Email"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Phone <span style="color: red">*</span>
                </template>
                <template #input>
                  <c-input
                    type="text"
                    placeholder="Type Phone"
                    v-model="doctorInfo.phone"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Other Info <span style="color: red">*</span>
                </template>
                <template #input>
                  <c-textarea
                    type="text"
                    placeholder="Other Info"
                    v-model="doctorInfo.other_info"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Comment <span style="color: red">*</span>
                </template>
                <template #input>
                  <c-textarea
                    type="text"
                    placeholder="Select Comment"
                    v-model="doctorInfo.comment"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
          </div>
        </c-card-body>
      </c-card>
      <c-card>
        <c-card-header
          ><strong
            ><c-icon class="icon" name="cil-hospital" /> Patient Information
          </strong></c-card-header
        >
        <c-card-body>
          <div class="row">
            <div class="col-lg-6 col-md-6 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Name <span style="color: red">*</span>
                </template>
                <template #input>
                  <c-input
                    type="text"
                    placeholder="Type Patient Name"
                    v-model="patientInfo.name"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Age Unit <span style="color: red">*</span>
                </template>
                <template #input>
                  <v-select
                    v-model="patientInfo.age_unit_id"
                    :options="ageUnits"
                    placeholder="Select Age Unit"
                    label="name"
                    :reduce="(ageUnit) => ageUnit.id"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Exact Age <span style="color: red">*</span>
                </template>
                <template #input>
                  <v-select
                    v-model="patientInfo.exact_age"
                    :options="exact_ages"
                    placeholder="Select Exact_age"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Age Group <span style="color: red">*</span>
                </template>
                <template #input>
                  <v-select
                    v-model="patientInfo.age_group_id"
                    label="name"
                    :reduce="(ageUnit) => ageUnit.id"
                    :options="ageGroups"
                    placeholder="Select Age Group"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Sex <span style="color: red">*</span>
                </template>
                <template #input>
                  <v-select
                    v-model="patientInfo.sex_id"
                    :options="sex"
                    placeholder="Select Patient Sex"
                    label="name"
                    :reduce="(sex) => sex.id"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label"> Medical History </template>
                <template #input>
                  <c-input
                    type="text"
                    placeholder="Type Medical History"
                    v-model="patientInfo.medical_history"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label"> Other Taking Medications </template>
                <template #input>
                  <c-input
                    type="text"
                    placeholder="Type Other Taking Medications"
                    v-model="patientInfo.taking_medications"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Comment <span style="color: red">*</span>
                </template>
                <template #input>
                  <c-textarea
                    type="text"
                    placeholder="Select Comment"
                    v-model="patientInfo.comment"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
          </div>
        </c-card-body>
      </c-card>
      <c-card>
        <c-card-header
          ><strong
            ><c-icon class="icon" name="cil-calendar" /> Adverse Event(s)
            Information</strong
          >
        </c-card-header>
        <c-card-body>
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Event (Language as reported) <span style="color: red">*</span>
                </template>
                <template #input>
                  <c-input
                    type="text"
                    placeholder="Type Event"
                    v-model="adverseEvent.narrative"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Reaction Event Start Date <span style="color: red">*</span>
                </template>
                <template #input>
                  <c-input
                    type="date"
                    v-model="adverseEvent.reaction_start_date"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Reaction Event End Date <span style="color: red">*</span>
                </template>
                <template #input>
                  <c-input
                    type="date"
                    v-model="adverseEvent.reaction_end_date"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Action Taken With Adwia Drug <span style="color: red">*</span>
                </template>
                <template #input>
                  <v-select
                    v-model="adverseEvent.action_id"
                    :options="actionDrugs"
                    placeholder="Select Action"
                    label="name"
                    :reduce="(actionDrug) => actionDrug.id"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-8">
              <c-form-group>
                <template slot="label">
                  Other Comment <span style="color: red">*</span>
                </template>
                <template #input>
                  <c-textarea
                    type="text"
                    placeholder="Select Comment"
                    v-model="adverseEvent.comment"
                    class="mt-2"
                  />
                </template>
              </c-form-group>
            </div>
          </div>
        </c-card-body>
      </c-card>
      <c-card>
        <c-card-header
          ><strong>
            <c-icon class="icon" name="cil-description" /> Drug(s) Information
          </strong></c-card-header
        >
        <c-card-body>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template slot="label">
                      Product Name <span style="color: red">*</span>
                    </template>
                    <template #input>
                      <v-select
                        v-model="drugInfo.product_id"
                        :options="products"
                        label="name"
                        :reduce="(product) => product.id"
                        placeholder="Select Product"
                        class="mt-2"
                        multiple
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template slot="label">
                      Dosages <span style="color: red">*</span>
                    </template>
                    <template #input>
                      <c-input
                        type="text"
                        placeholder="Type Dosages"
                        v-model="drugInfo.dosages"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template slot="label">
                      Route Administration 
                    </template>
                    <template #input>
                      <c-input
                        type="text"
                        placeholder="Type Route Administration"
                        v-model="drugInfo.route_adminstration"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template slot="label">
                      Indication the drug used for
                      <span style="color: red">*</span>
                    </template>
                    <template #input>
                      <c-input
                        type="text"
                        placeholder="Type Indication"
                        v-model="drugInfo.indication_for"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template slot="label">
                      Start Date Of Taking Drug <span style="color: red">*</span>
                    </template>
                    <template #input>
                      <c-input
                        type="date"
                        v-model="drugInfo.start_date"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template slot="label"> End Date Of Taking Drug  <span style="color: red">*</span></template>
                    <template #input>
                      <c-input
                        type="date"
                        v-model="drugInfo.end_date"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template slot="label"> Re-taking Drug </template>
                    <template #input>
                      <v-select
                        v-model="drugInfo.retake"
                        :options="options"
                        placeholder="Select Retake"
                        label="name"
                        :reduce="(option) => option.value"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template slot="label"> Event Outcome </template>
                    <template #input>
                      <c-textarea
                        type="text"
                        v-model="drugInfo.event_outcome"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template slot="label"> Event Happened Again </template>
                    <template #input>
                      <v-select
                        v-model="drugInfo.is_happened_again"
                        :options="options"
                        placeholder="Select Happen"
                        label="name"
                        :reduce="(option) => option.value"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template slot="label">
                      Other Comment <span style="color: red">*</span>
                    </template>
                    <template #input>
                      <c-textarea
                        type="text"
                        placeholder="Select Comment"
                        v-model="drugInfo.comment"
                        class="mt-2"
                      />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-card-body>
      </c-card>
      <c-card>
        <c-card-header
          ><strong
            ><c-icon class="icon" name="cil-plus" /> Additional Information
          </strong></c-card-header
        >
        <c-card-body>
          <div class="row">
            <div class="col-lg-12 col-md-8 col-sm-8">
              <c-textarea
                    type="text"
                    placeholder="Type Info"
                    v-model="additional.info"
                    class="mt-2"
                  />
            </div>
          </div>
        </c-card-body>
      </c-card>
    </c-card-body>
    <c-card-footer>
      <c-button style="float: right" color="primary" @click="save"
        >Save</c-button
      >
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data: () => {
    return {
      visit: null,
      options: [
        { value: 1, name: "Yes" },
        { value: 0, name: "No" },
      ],
      pv: {
        user_id: null,
        visit_id: null,
        pv_module_id: null,
        submition_date: null,
      },
      doctorInfo: {
        pv_id: null,
        doctor_id: null,
        email: null,
        phone: null,
        qualification_id: null,
        other_info: null,
        comment: null,
      },
      patientInfo: {
        pv_id: null,
        name: null,
        age_unit_id: null,
        exact_age: null,
        age_group_id: null,
        sex_id: null,
        medical_history: null,
        taking_medications: null,
        comment: null,
      },
      adverseEvent: {
        pv_id: null,
        narrative: null,
        action_id: null,
        reaction_start_date: null,
        reaction_end_date: null,
        comment: null,
      },
      products: [],
      pvModules: [],
      doctorQualifications: [],
      actionDrugs: [],
      sex: [],
      ageUnits: [],
      ageGroups: [],
      products: [],
      drugInfo: {
        pv_id: null,
        product_id: null,
        dosages: null,
        route_adminstration: null,
        indication_for: null,
        start_date: null,
        end_date: null,
        retake: null,
        event_outcome: null,
        is_happened_again: null,
        comment: null,
      },
      additional: {
        visit_id: null,
        pv_module_id: null,
        info: null
      },
      exact_ages: Array.from({ length: 100 }, (_, i) => i + 1), // Creates [1, 2, ..., 100]
    };
  },

  methods: {
    async initialize() {
      await axios
        .get(`/api/pv-data`)
        .then((response) => {
          this.pvModules = response.data.data.pvModules;
          this.doctorQualifications = response.data.data.doctorQualifications;
          this.ageGroups = response.data.data.ageGroups;
          this.ageUnits = response.data.data.ageUnits;
          this.actionDrugs = response.data.data.actionDrugs;
          this.sex = response.data.data.sex;
          this.pv.user_id = response.data.data.user_id;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
      await axios
        .get(`/api/actual_visits/${this.$route.params.id}`)
        .then((response) => {
          this.visit = response.data.actual_visit;
          this.doctorInfo.doctor_id = this.visit.doctor_id;
          this.pv.visit_id = this.visit.id;
          this.products = response.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    save() {
      // console.log(this.drugInfo);
      const drugList = this.drugInfo.product_id.map((id) => ({
        ...this.drugInfo,
        product_id: id,
      }));
      axios
        .post(`/api/save-pv`, {
          pv: this.pv,
          doctorInfo: this.doctorInfo,
          patientInfo: this.patientInfo,
          adverseEvent: this.adverseEvent,
          drugInfo: drugList,
          doctorInfo: this.doctorInfo,
          additional: this.additional,
        })
        .then((response) => {
          this.flash("PV Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
<style>
.icon {
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
  margin-bottom: 3px !important;
  color: #e09898 !important;
}
</style>
