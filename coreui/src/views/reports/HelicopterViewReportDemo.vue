<template>
  <div>
    <h2>Helicopter View Report Demo</h2>
    <p>This is a demo page to test the Helicopter View Report component.</p>
    
    <!-- Include the actual component -->
    <helicopter-view-report />
    
    <!-- Demo API Response -->
    <c-card class="mt-4">
      <c-card-header>
        <h5>Expected API Response Format</h5>
      </c-card-header>
      <c-card-body>
        <pre>{{ demoApiResponse }}</pre>
      </c-card-body>
    </c-card>
    
    <!-- Demo Request -->
    <c-card class="mt-4">
      <c-card-header>
        <h5>Expected API Request Format</h5>
      </c-card-header>
      <c-card-body>
        <pre>{{ demoApiRequest }}</pre>
      </c-card-body>
    </c-card>
  </div>
</template>

<script>
import HelicopterViewReport from './HelicopterViewReport.vue';

export default {
  name: 'HelicopterViewReportDemo',
  components: {
    HelicopterViewReport,
  },
  data() {
    return {
      demoApiRequest: {
        "from_date": "2024-01-01",
        "to_date": "2024-01-31", 
        "line_ids": [1, 2, 3]
      },
      demoApiResponse: {
        "data": [
          {
            "id": 1,
            "line_name": "Line A",
            "total_visits": 150,
            "total_sales": 25000.50,
            "coverage_percentage": 85.5,
            "date": "2024-01-15"
          },
          {
            "id": 2,
            "line_name": "Line B", 
            "total_visits": 200,
            "total_sales": 35000.75,
            "coverage_percentage": 92.3,
            "date": "2024-01-15"
          }
        ],
        "summary": {
          "total_records": 2,
          "lines_count": 3,
          "total_visits": 350,
          "total_sales": 60001.25
        },
        "fields": [
          {
            "key": "line_name",
            "label": "Line Name",
            "class": ""
          },
          {
            "key": "total_visits", 
            "label": "Total Visits",
            "class": "text-center"
          },
          {
            "key": "total_sales",
            "label": "Total Sales",
            "class": "text-end"
          },
          {
            "key": "coverage_percentage",
            "label": "Coverage %",
            "class": "text-center"
          },
          {
            "key": "date",
            "label": "Date",
            "class": "text-center"
          }
        ]
      }
    };
  },
};
</script>

<style scoped>
pre {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  font-size: 0.875rem;
}
</style>
