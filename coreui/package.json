{"name": "@coreui/coreui-pro-vue-laravel-admin-template", "version": "1.0.0", "description": "CoreUI Pro Vue Laravel Bootstrap Admin Template", "author": {"name": "CoreUI", "url": "https://coreui.io", "github": "https://github.com/coreui", "twitter": "https://twitter.com/core_ui"}, "contributors": [{"name": "CoreUI Team", "url": "https://github.com/orgs/coreui/people"}], "homepage": "http://coreui.io", "copyright": "Copyright 2020 creativeLabs <PERSON><PERSON>", "repository": {"type": "git", "url": "**************:coreui/coreui-pro-vue-laravel-admin-template.git"}, "license": "https://coreui.io/pro/license/", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "watch": "vue-cli-service watch", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "clearCache": "jest --clear<PERSON>ache", "release": "npm-run-all clearCache lint build test:unit test:e2e"}, "dependencies": {"@coreui/coreui-pro": "^3.4.2", "@coreui/utils": "^1.3.1", "@coreui/vue": "^3.2.11", "@googlemaps/markerclusterer": "^2.5.0", "@johmun/vue-tags-input": "^2.1.0", "@mdi/font": "^5.9.55", "@syncfusion/ej2-vue-buttons": "^22.2.9", "@syncfusion/ej2-vue-calendars": "^22.2.10", "@syncfusion/ej2-vue-diagrams": "^22.2.12", "@syncfusion/ej2-vue-dropdowns": "^22.2.10", "@syncfusion/ej2-vue-grids": "^22.2.12", "@syncfusion/ej2-vue-inputs": "^22.2.9", "@syncfusion/ej2-vue-layouts": "^22.2.8", "@syncfusion/ej2-vue-navigations": "^22.2.8", "@syncfusion/ej2-vue-popups": "^22.2.11", "@syncfusion/ej2-vue-schedule": "^22.2.8", "@syncfusion/ej2-vue-splitbuttons": "^22.2.8", "@types/simple-peer": "^9.11.4", "ag-grid-community": "^28.2.1", "ag-grid-enterprise": "^28.2.1", "ag-grid-vue": "^28.2.1", "axios": "^0.21.4", "body-scroll-lock": "^4.0.0-beta.0", "echarts": "^5.4.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "firebase": "^10.1.0", "int64-buffer": "^1.0.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.29", "jspdf-html2canvas": "^1.4.9", "leaflet": "^1.9.4", "luxon": "^1.28.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "quill": "~1.3.7", "sass": "^1.63.3", "save": "^2.9.0", "simple-peer": "^9.11.1", "socket.io-client": "^4.4.1", "streamsaver": "^2.0.6", "vee-validate": "^3.4.11", "vue": "^2.7.0", "vue-color": "^2.8.1", "vue-datetime": "^1.0.0-beta.14", "vue-echarts": "^6.2.3", "vue-html-to-paper": "^1.4.4", "vue-loader": "^15.10.0", "vue-property-decorator": "^8.5.1", "vue-quill-editor": "~3.0.6", "vue-router": "~3.1.6", "vue-select": "^3.12.2", "vue-text-mask": "~6.1.2", "vue2-timepicker": "^1.1.6", "vuetify": "^2.5.8", "vuex": "~3.1.3", "vuex-i18n": "^1.13.1", "web-streams-polyfill": "^3.2.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "~7.22.5", "@babel/eslint-parser": "~7.22.5", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-e2e-nightwatch": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-unit-jest": "~5.0.8", "@vue/cli-service": "^5.0.8", "@vue/test-utils": "1.0.0-beta.29", "babel-jest": "~29.5.0", "chromedriver": "^114.0.2", "eslint": "~8.42.0", "eslint-plugin-vue": "~9.14.1", "node-sass": "^8.0.0", "npm-run-all": "~4.1.5", "sass-loader": "^13.3.2", "script-loader": "^0.7.2"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 9"], "engines": {"node": ">= 8.10.x", "npm": ">= 5.6.0"}}