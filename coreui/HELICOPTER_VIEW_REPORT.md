# Helicopter View Report

## Overview
The Helicopter View Report is a comprehensive dashboard component that provides high-level insights across multiple business lines. It consumes the POST `/api/helicopter-view-report` endpoint to generate reports with customizable date ranges and line selections.

## Features
- **Date Range Selection**: Choose from and to dates for report generation
- **Multi-Line Selection**: Select one or multiple business lines with "Select All" functionality
- **Real-time Data**: Generate reports on-demand with loading states
- **Summary Cards**: Display key metrics and statistics
- **Data Table**: Professional table with sticky headers and responsive design
- **Export Options**: Download as Excel and print functionality
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Responsive Design**: Works on desktop, tablet, and mobile devices

## API Integration

### Request Format
```javascript
POST /api/helicopter-view-report
{
  "from_date": "2024-01-01",
  "to_date": "2024-01-31", 
  "line_ids": [1, 2, 3]
}
```

### Expected Response Format
```javascript
{
  "data": [
    {
      "id": 1,
      "line_name": "Line A",
      "total_visits": 150,
      "total_sales": 25000.50,
      "coverage_percentage": 85.5,
      "date": "2024-01-15"
    }
  ],
  "summary": {
    "total_records": 2,
    "lines_count": 3,
    "total_visits": 350,
    "total_sales": 60001.25
  },
  "fields": [
    {
      "key": "line_name",
      "label": "Line Name",
      "class": ""
    },
    {
      "key": "total_sales",
      "label": "Total Sales",
      "class": "text-end"
    }
  ]
}
```

## Component Usage

### Basic Usage
```vue
<template>
  <helicopter-view-report />
</template>

<script>
import HelicopterViewReport from '@/views/reports/HelicopterViewReport.vue';

export default {
  components: {
    HelicopterViewReport,
  },
};
</script>
```

### Route Configuration
The component is automatically registered in the reports routing system:

```javascript
{
  path: 'helicopter-view-report',
  meta: { label: 'Helicopter View Report', permission: 'show_reports_helicopter_view' },
  name: 'helicopter-view-report',
  component: () => import('./../../views/reports/HelicopterViewReport.vue'),
}
```

## File Structure
```
coreui/src/views/reports/
├── HelicopterViewReport.vue          # Main component
├── HelicopterViewReportDemo.vue      # Demo/test component
└── reportsData.js                    # Updated with new report entry

coreui/src/router/reports/
└── generalReports.routes.js          # Updated with new routes
```

## Features Breakdown

### 1. Filters Section
- **Date Range**: From/To date inputs with validation
- **Line Selection**: Multi-select dropdown with "Select All" checkbox
- **Action Buttons**: Reset and Generate Report buttons

### 2. Loading States
- Spinner during report generation
- Loading badges in header
- Disabled states for buttons during loading

### 3. Summary Cards
- Total Records count
- Lines Covered count  
- Date Range duration
- Last Updated timestamp

### 4. Data Table
- Sticky headers for better navigation
- Responsive design with horizontal scrolling
- Auto-formatted columns (currency, dates, percentages)
- Professional styling with hover effects

### 5. Export Features
- **Excel Download**: Uses existing downloadXlsx mixin
- **Print**: Uses $htmlToPaper for printing
- Timestamped filenames

### 6. Error Handling
- Network error handling
- Validation error display
- User-friendly error messages
- Fallback states

## Styling
The component uses CoreUI design patterns with:
- Professional card layouts
- Consistent color scheme
- Responsive grid system
- Smooth animations and transitions
- Print-friendly styles

## Dependencies
- **moment**: Date formatting and manipulation
- **vue-select**: Multi-select dropdown component
- **axios**: HTTP requests (via global mixin)
- **CoreUI**: UI components and styling

## Permissions
The component requires the `show_reports_helicopter_view` permission to be accessible in the reports menu.

## Testing
A demo component is available at `/reports/helicopter-view-demo` for testing the component with sample data.

## Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design
- Print functionality
- Excel download support
